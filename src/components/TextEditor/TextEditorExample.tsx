'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import TextEditor from './TextEditor';
import { createImageUploadFunction, type ImageResizeOptions } from '@/utils/imageUpload';
import { appTheme } from '@/app/theme';

const ExampleContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
`;

const Title = styled.h1`
  color: ${appTheme.colors.text.primary};
  margin-bottom: 20px;
`;

const Section = styled.div`
  margin-bottom: 30px;
`;

const SectionTitle = styled.h2`
  color: ${appTheme.colors.text.primary};
  margin-bottom: 15px;
  font-size: 1.2rem;
`;

const Description = styled.p`
  color: ${appTheme.colors.text.secondary};
  margin-bottom: 15px;
  line-height: 1.5;
`;

const ConfigSection = styled.div`
  background: #f9fafb;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
`;

const ConfigTitle = styled.h3`
  margin: 0 0 10px 0;
  color: ${appTheme.colors.text.primary};
`;

const ConfigGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
`;

const ConfigItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`;

const Label = styled.label`
  font-size: 0.9rem;
  color: ${appTheme.colors.text.secondary};
  font-weight: 500;
`;

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.9rem;
`;

const Select = styled.select`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.9rem;
`;

const Checkbox = styled.input`
  margin-right: 8px;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: ${appTheme.colors.text.secondary};
`;

const OutputSection = styled.div`
  margin-top: 20px;
  padding: 15px;
  background: #f3f4f6;
  border-radius: 8px;
`;

const OutputTitle = styled.h3`
  margin: 0 0 10px 0;
  color: ${appTheme.colors.text.primary};
`;

const OutputContent = styled.pre`
  background: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
`;

export default function TextEditorExample() {
  const [content, setContent] = useState('<p>Try uploading an image to see the automatic resizing in action!</p>');
  const [maxWidth, setMaxWidth] = useState(800);
  const [maxHeight, setMaxHeight] = useState(600);
  const [quality, setQuality] = useState(0.85);
  const [format, setFormat] = useState<'jpeg' | 'png' | 'webp'>('jpeg');
  const [enableResize, setEnableResize] = useState(true);

  // Mock upload function for demonstration
  const mockUploadFunction = async (file: File): Promise<string> => {
    // In a real app, this would upload to your server
    // For demo purposes, we'll just return a data URL
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    });
  };

  const imageResizeOptions: ImageResizeOptions = {
    maxWidth,
    maxHeight,
    quality,
    format,
  };

  return (
    <ExampleContainer>
      <Title>TextEditor with Image Resizing</Title>
      
      <Description>
        This example demonstrates the TextEditor component with automatic image resizing functionality.
        Upload an image to see how it gets automatically resized according to your settings.
      </Description>

      <Section>
        <SectionTitle>Configuration</SectionTitle>
        <ConfigSection>
          <ConfigTitle>Image Resize Settings</ConfigTitle>
          <ConfigGrid>
            <ConfigItem>
              <Label htmlFor="maxWidth">Max Width (px)</Label>
              <Input
                id="maxWidth"
                type="number"
                value={maxWidth}
                onChange={(e) => setMaxWidth(Number(e.target.value))}
                min="100"
                max="2000"
              />
            </ConfigItem>
            <ConfigItem>
              <Label htmlFor="maxHeight">Max Height (px)</Label>
              <Input
                id="maxHeight"
                type="number"
                value={maxHeight}
                onChange={(e) => setMaxHeight(Number(e.target.value))}
                min="100"
                max="2000"
              />
            </ConfigItem>
            <ConfigItem>
              <Label htmlFor="quality">Quality (0.1 - 1.0)</Label>
              <Input
                id="quality"
                type="number"
                value={quality}
                onChange={(e) => setQuality(Number(e.target.value))}
                min="0.1"
                max="1.0"
                step="0.05"
              />
            </ConfigItem>
            <ConfigItem>
              <Label htmlFor="format">Output Format</Label>
              <Select
                id="format"
                value={format}
                onChange={(e) => setFormat(e.target.value as 'jpeg' | 'png' | 'webp')}
              >
                <option value="jpeg">JPEG</option>
                <option value="png">PNG</option>
                <option value="webp">WebP</option>
              </Select>
            </ConfigItem>
          </ConfigGrid>
          <div style={{ marginTop: '15px' }}>
            <CheckboxLabel>
              <Checkbox
                type="checkbox"
                checked={enableResize}
                onChange={(e) => setEnableResize(e.target.checked)}
              />
              Enable automatic image resizing
            </CheckboxLabel>
          </div>
        </ConfigSection>
      </Section>

      <Section>
        <SectionTitle>Text Editor</SectionTitle>
        <TextEditor
          value={content}
          onChange={setContent}
          placeholder="Start typing or upload an image..."
          minHeight={200}
          maxHeight={400}
          uploadImage={mockUploadFunction}
          imageResizeOptions={imageResizeOptions}
          enableImageResize={enableResize}
        />
      </Section>

      <OutputSection>
        <OutputTitle>Generated HTML Content</OutputTitle>
        <OutputContent>{content}</OutputContent>
      </OutputSection>
    </ExampleContainer>
  );
}
