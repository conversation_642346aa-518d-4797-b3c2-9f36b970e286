/**
 * Image resize options
 */
export interface ImageResizeOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
}

/**
 * Resize an image file while maintaining aspect ratio
 */
export async function resizeImage(
  file: File,
  options: ImageResizeOptions = {}
): Promise<File> {
  const {
    maxWidth = 800,
    maxHeight = 600,
    quality = 0.85,
    format = 'jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img;

        // Only resize if image is larger than max dimensions
        if (width <= maxWidth && height <= maxHeight) {
          // Image is already small enough, return original
          resolve(file);
          return;
        }

        // Calculate scaling factor
        const scaleX = maxWidth / width;
        const scaleY = maxHeight / height;
        const scale = Math.min(scaleX, scaleY);

        // Calculate new dimensions
        const newWidth = Math.floor(width * scale);
        const newHeight = Math.floor(height * scale);

        // Set canvas dimensions
        canvas.width = newWidth;
        canvas.height = newHeight;

        // Draw resized image
        ctx?.drawImage(img, 0, 0, newWidth, newHeight);

        // Convert to blob
        const outputFormat = format === 'png' ? 'image/png' :
                           format === 'webp' ? 'image/webp' : 'image/jpeg';

        canvas.toBlob((blob) => {
          if (blob) {
            // Create new file with resized image
            const resizedFile = new File([blob], file.name, {
              type: outputFormat,
              lastModified: Date.now(),
            });
            resolve(resizedFile);
          } else {
            reject(new Error('Failed to create resized image blob'));
          }
        }, outputFormat, quality);

      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    // Load the image
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Check if a file is a supported image format
 */
export function isSupportedImageFormat(file: File): boolean {
  const supportedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif'
  ];
  return supportedTypes.includes(file.type.toLowerCase());
}

/**
 * Upload image to server and return the URL
 */
export async function uploadImageToServer(file: File, token: string): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('fileType', 'images');

  const response = await fetch('/api/v1/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Upload failed (${response.status}): ${errorText}`);
  }

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Upload failed');
  }

  if (!result.data?.url) {
    throw new Error('No URL returned from upload');
  }

  return result.data.url;
}

/**
 * Extract base64 images from HTML content and upload them to server
 * Replace base64 data URLs with server URLs
 */
export async function processImagesInContent(
  htmlContent: string, 
  token: string
): Promise<string> {
  // Find all img tags with base64 data URLs
  const imgRegex = /<img[^>]+src="data:image\/[^;]+;base64,[^"]*"[^>]*>/g;
  const matches = htmlContent.match(imgRegex);
  
  if (!matches) {
    return htmlContent;
  }

  let processedContent = htmlContent;

  // Process each image
  for (const imgTag of matches) {
    try {
      // Extract the base64 data URL
      const srcMatch = imgTag.match(/src="(data:image\/[^;]+;base64,[^"]*)"/);
      if (!srcMatch) continue;

      const dataUrl = srcMatch[1];
      
      // Convert base64 to File
      const file = await dataUrlToFile(dataUrl);
      
      // Upload to server
      const serverUrl = await uploadImageToServer(file, token);
      
      // Replace the base64 URL with server URL
      const newImgTag = imgTag.replace(dataUrl, serverUrl);
      processedContent = processedContent.replace(imgTag, newImgTag);
      
    } catch (error) {
      console.error('Failed to process image:', error);
      // Keep the original base64 image if upload fails
    }
  }

  return processedContent;
}

/**
 * Convert data URL to File object
 */
async function dataUrlToFile(dataUrl: string): Promise<File> {
  const response = await fetch(dataUrl);
  const blob = await response.blob();
  
  // Extract mime type and create filename
  const mimeType = dataUrl.split(';')[0].split(':')[1];
  const extension = mimeType.split('/')[1];
  const filename = `image_${Date.now()}.${extension}`;
  
  return new File([blob], filename, { type: mimeType });
}

/**
 * Resize and upload image to server
 */
export async function resizeAndUploadImage(
  file: File,
  token: string,
  resizeOptions?: ImageResizeOptions
): Promise<string> {
  // Check if it's a supported image format
  if (!isSupportedImageFormat(file)) {
    throw new Error('Unsupported image format. Please use JPEG, PNG, WebP, or GIF.');
  }

  // Resize the image
  const resizedFile = await resizeImage(file, resizeOptions);

  // Upload the resized image
  return await uploadImageToServer(resizedFile, token);
}

/**
 * Create upload function for TextEditor component with automatic resizing
 */
export function createImageUploadFunction(token: string, resizeOptions?: ImageResizeOptions) {
  return async (file: File): Promise<string> => {
    return await resizeAndUploadImage(file, token, resizeOptions);
  };
}

/**
 * Create upload function for TextEditor component (legacy - without resizing)
 */
export function createLegacyImageUploadFunction(token: string) {
  return async (file: File): Promise<string> => {
    return await uploadImageToServer(file, token);
  };
}
